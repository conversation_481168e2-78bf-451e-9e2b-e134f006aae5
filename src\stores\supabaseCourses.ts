import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { supabase } from '@/lib/supabase'
import type { Database } from '@/types/database'

type Course = Database['public']['Tables']['courses']['Row']
type Category = Database['public']['Tables']['categories']['Row']
type Enrollment = Database['public']['Tables']['enrollments']['Row']

export const useSupabaseCoursesStore = defineStore('supabaseCourses', () => {
  const courses = ref<Course[]>([])
  const categories = ref<Category[]>([])
  const enrollments = ref<Enrollment[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  const featuredCourses = computed(() => 
    courses.value.filter(course => course.is_featured && course.status === 'published').slice(0, 6)
  )

  const popularCourses = computed(() => 
    courses.value
      .filter(course => course.status === 'published')
      .sort((a, b) => b.enrollment_count - a.enrollment_count)
      .slice(0, 8)
  )

  const publishedCourses = computed(() =>
    courses.value.filter(course => course.status === 'published')
  )

  // Fetch all published courses
  async function fetchCourses() {
    loading.value = true
    error.value = null
    
    try {
      const { data, error: fetchError } = await supabase
        .from('courses')
        .select(`
          *,
          profiles:instructor_id (
            id,
            full_name,
            avatar_url
          ),
          categories:category_id (
            id,
            name,
            slug
          )
        `)
        .eq('status', 'published')
        .order('created_at', { ascending: false })

      if (fetchError) throw fetchError
      courses.value = data || []
    } catch (err) {
      error.value = 'Failed to fetch courses'
      console.error('Error fetching courses:', err)
    } finally {
      loading.value = false
    }
  }

  // Fetch categories
  async function fetchCategories() {
    try {
      const { data, error: fetchError } = await supabase
        .from('categories')
        .select('*')
        .eq('is_active', true)
        .order('sort_order', { ascending: true })

      if (fetchError) throw fetchError
      categories.value = data || []
    } catch (err) {
      console.error('Error fetching categories:', err)
    }
  }

  // Fetch user enrollments
  async function fetchUserEnrollments(userId: string) {
    try {
      const { data, error: fetchError } = await supabase
        .from('enrollments')
        .select(`
          *,
          courses (
            id,
            title,
            thumbnail_url,
            instructor_id
          )
        `)
        .eq('student_id', userId)
        .eq('status', 'active')

      if (fetchError) throw fetchError
      enrollments.value = data || []
    } catch (err) {
      console.error('Error fetching enrollments:', err)
    }
  }

  // Get course by ID
  async function getCourseById(id: string) {
    try {
      const { data, error: fetchError } = await supabase
        .from('courses')
        .select(`
          *,
          profiles:instructor_id (
            id,
            full_name,
            avatar_url,
            bio
          ),
          categories:category_id (
            id,
            name,
            slug
          ),
          course_modules (
            id,
            title,
            description,
            sort_order,
            lessons (
              id,
              title,
              description,
              duration_seconds,
              sort_order,
              is_preview
            )
          )
        `)
        .eq('id', id)
        .eq('status', 'published')
        .single()

      if (fetchError) throw fetchError
      return data
    } catch (err) {
      console.error('Error fetching course:', err)
      return null
    }
  }

  // Search courses
  async function searchCourses(query: string) {
    try {
      const { data, error: fetchError } = await supabase
        .from('courses')
        .select(`
          *,
          profiles:instructor_id (
            id,
            full_name,
            avatar_url
          ),
          categories:category_id (
            id,
            name,
            slug
          )
        `)
        .eq('status', 'published')
        .or(`title.ilike.%${query}%,description.ilike.%${query}%`)
        .order('created_at', { ascending: false })

      if (fetchError) throw fetchError
      return data || []
    } catch (err) {
      console.error('Error searching courses:', err)
      return []
    }
  }

  // Get courses by category
  async function getCoursesByCategory(categoryId: string) {
    try {
      const { data, error: fetchError } = await supabase
        .from('courses')
        .select(`
          *,
          profiles:instructor_id (
            id,
            full_name,
            avatar_url
          )
        `)
        .eq('category_id', categoryId)
        .eq('status', 'published')
        .order('created_at', { ascending: false })

      if (fetchError) throw fetchError
      return data || []
    } catch (err) {
      console.error('Error fetching courses by category:', err)
      return []
    }
  }

  // Enroll in course
  async function enrollInCourse(courseId: string, studentId: string) {
    try {
      const { data, error: enrollError } = await supabase
        .from('enrollments')
        .insert({
          course_id: courseId,
          student_id: studentId,
          status: 'active'
        })
        .select()
        .single()

      if (enrollError) throw enrollError

      // Update course enrollment count
      await supabase.rpc('increment_enrollment_count', { course_id: courseId })

      return { data, error: null }
    } catch (err) {
      console.error('Error enrolling in course:', err)
      return { data: null, error: err }
    }
  }

  return {
    courses,
    categories,
    enrollments,
    loading,
    error,
    featuredCourses,
    popularCourses,
    publishedCourses,
    fetchCourses,
    fetchCategories,
    fetchUserEnrollments,
    getCourseById,
    searchCourses,
    getCoursesByCategory,
    enrollInCourse
  }
})
