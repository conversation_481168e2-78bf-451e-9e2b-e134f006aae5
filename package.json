{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@supabase/supabase-js": "^2.54.0", "@vitejs/plugin-vue": "^5.2.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-vue-next": "^0.537.0", "pinia": "^2.3.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "vue": "^3.5.18", "vue-i18n": "^9.14.5", "vue-router": "^4.5.1", "zod": "^3.25.76"}, "devDependencies": {"@eslint/js": "^9.32.0", "@tailwindcss/typography": "^0.5.16", "@types/node": "^22.16.5", "autoprefixer": "^10.4.21", "eslint": "^9.32.0", "globals": "^15.15.0", "lovable-tagger": "^1.1.8", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "typescript-eslint": "^8.38.0", "vite": "^5.4.19"}}