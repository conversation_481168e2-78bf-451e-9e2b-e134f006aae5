<template>
  <div class="group bg-card rounded-lg border shadow-sm hover:shadow-elevated transition-all duration-300 overflow-hidden">
    <div class="relative overflow-hidden">
      <img
        :src="imageUrl"
        :alt="title"
        class="h-48 w-full object-cover transition-transform duration-300 group-hover:scale-110"
      />
      <div class="absolute top-4 right-4">
        <span class="bg-background/90 text-foreground px-2 py-1 rounded-full text-xs font-medium">
          {{ level }}
        </span>
      </div>
      <div v-if="originalPrice" class="absolute top-4 left-4">
        <span class="bg-destructive text-destructive-foreground px-2 py-1 rounded-full text-xs font-medium">
          {{ Math.round(((originalPrice - price) / originalPrice) * 100) }}% OFF
        </span>
      </div>
    </div>
    
    <div class="p-4">
      <div class="flex items-center gap-2 mb-2">
        <img
          :src="instructor.avatar"
          :alt="instructor.name"
          class="h-6 w-6 rounded-full"
        />
        <span class="text-sm text-muted-foreground">{{ instructor.name }}</span>
      </div>
      
      <h3 class="font-semibold text-lg mb-2 line-clamp-2 group-hover:text-primary transition-colors">
        {{ title }}
      </h3>
      
      <p class="text-sm text-muted-foreground mb-3 line-clamp-2">
        {{ description }}
      </p>
      
      <div class="flex items-center gap-1 mb-3">
        <div class="flex">
          <Star
            v-for="i in 5"
            :key="i"
            class="h-4 w-4"
            :class="i <= Math.floor(rating) ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'"
          />
        </div>
        <span class="text-sm font-medium">{{ rating }}</span>
        <span class="text-sm text-muted-foreground">({{ reviewCount?.toLocaleString() }})</span>
      </div>
      
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-2">
          <span class="font-bold text-lg">${{ price }}</span>
          <span v-if="originalPrice" class="text-sm text-muted-foreground line-through">
            ${{ originalPrice }}
          </span>
        </div>
        <router-link
          :to="`/course/${id}`"
          class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground shadow hover:bg-primary/90 h-9 px-3"
        >
          View Course
        </router-link>
      </div>
      
      <div class="flex items-center justify-between mt-3 pt-3 border-t text-xs text-muted-foreground">
        <span>{{ studentCount?.toLocaleString() }} students</span>
        <span>{{ duration }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Star } from 'lucide-vue-next'

interface Props {
  id: number
  title: string
  description: string
  instructor: {
    name: string
    avatar: string
  }
  rating: number
  reviewCount: number
  price: number
  originalPrice?: number
  duration: string
  studentCount: number
  imageUrl: string
  level: string
}

defineProps<Props>()
</script>

<style>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>