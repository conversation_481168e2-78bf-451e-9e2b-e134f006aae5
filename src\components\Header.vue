<template>
  <header class="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
    <div class="container flex h-16 items-center justify-between">
      <div class="flex items-center gap-6">
        <router-link to="/" class="flex items-center gap-2">
          <div class="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-primary">
            <BookOpen class="h-5 w-5 text-primary-foreground" />
          </div>
          <span class="font-bold text-xl bg-gradient-primary bg-clip-text text-transparent">
            LearnHub
          </span>
        </router-link>

        <nav class="hidden md:flex items-center gap-6">
          <router-link to="/courses" class="text-sm font-medium hover:text-primary transition-colors">
            {{ $t('nav.courses') }}
          </router-link>
          <router-link to="/categories" class="text-sm font-medium hover:text-primary transition-colors">
            {{ $t('nav.categories') }}
          </router-link>
          <router-link to="/about" class="text-sm font-medium hover:text-primary transition-colors">
            {{ $t('nav.about') }}
          </router-link>
        </nav>
      </div>

      <div class="flex items-center gap-4">
        <!-- Search -->
        <div class="relative hidden sm:block">
          <Search class="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <input
            v-model="searchQuery"
            :placeholder="$t('hero.searchPlaceholder')"
            class="w-64 pl-10 h-9 px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 border border-input bg-transparent rounded-md"
          />
        </div>

        <!-- Language Switcher -->
        <div class="relative">
          <button
            @click="toggleLanguageMenu"
            class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-9 px-3"
          >
            <Globe class="h-4 w-4 mr-2" />
            {{ currentLanguage === 'en' ? 'English' : 'አማርኛ' }}
          </button>
          <div
            v-if="showLanguageMenu"
            class="absolute right-0 mt-2 w-40 bg-popover border border-border rounded-md shadow-lg z-50"
          >
            <button
              @click="changeLanguage('en')"
              class="w-full text-left px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground"
            >
              English
            </button>
            <button
              @click="changeLanguage('am')"
              class="w-full text-left px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground"
            >
              አማርኛ
            </button>
          </div>
        </div>

        <!-- Auth Buttons or User Menu -->
        <div v-if="!authStore.isAuthenticated" class="flex items-center gap-2">
          <router-link
            to="/auth/login"
            class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-9 px-3"
          >
            {{ $t('nav.login') }}
          </router-link>
          <router-link
            to="/auth/register"
            class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground shadow hover:bg-primary/90 h-9 px-3"
          >
            {{ $t('nav.signup') }}
          </router-link>
        </div>

        <!-- User Menu -->
        <div v-else class="relative">
          <button
            @click="toggleUserMenu"
            class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-9 w-9"
          >
            <User class="h-4 w-4" />
          </button>
          <div
            v-if="showUserMenu"
            class="absolute right-0 mt-2 w-48 bg-popover border border-border rounded-md shadow-lg z-50"
          >
            <router-link
              :to="authStore.isStudent ? '/student' : '/instructor'"
              class="block px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground"
            >
              {{ $t('nav.dashboard') }}
            </router-link>
            <router-link
              :to="authStore.isStudent ? '/student/enrollments' : '/instructor/courses'"
              class="block px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground"
            >
              {{ $t('nav.myCourses') }}
            </router-link>
            <button
              @click="logout"
              class="w-full text-left px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground"
            >
              {{ $t('nav.logout') }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { BookOpen, Search, Globe, User } from 'lucide-vue-next'

const { locale, t } = useI18n()
const router = useRouter()
const authStore = useAuthStore()

const searchQuery = ref('')
const showLanguageMenu = ref(false)
const showUserMenu = ref(false)

const currentLanguage = computed(() => locale.value)

function toggleLanguageMenu() {
  showLanguageMenu.value = !showLanguageMenu.value
  showUserMenu.value = false
}

function toggleUserMenu() {
  showUserMenu.value = !showUserMenu.value
  showLanguageMenu.value = false
}

function changeLanguage(lang: string) {
  locale.value = lang
  showLanguageMenu.value = false
}

function logout() {
  authStore.logout()
  router.push('/')
  showUserMenu.value = false
}

onMounted(() => {
  authStore.initAuth()
})
</script>