<template>
  <div>
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-3xl font-bold">My Courses</h1>
      <router-link to="/instructor/courses/create" class="bg-primary text-white px-4 py-2 rounded-md">
        Create Course
      </router-link>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <CourseCard v-for="course in myCourses" :key="course.id" v-bind="course" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useCoursesStore } from '@/stores/courses'
import CourseCard from '@/components/CourseCard.vue'

const authStore = useAuthStore()
const coursesStore = useCoursesStore()

const myCourses = computed(() => 
  coursesStore.getCoursesByInstructor(authStore.user?.id || 0)
)
</script>