<template>
  <div class="bg-card rounded-lg shadow-lg p-8">
    <h1 class="text-2xl font-bold text-center mb-6">{{ $t('auth.login') }}</h1>
    <form @submit.prevent="handleLogin">
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium mb-2">{{ $t('auth.email') }}</label>
          <input v-model="form.email" type="email" required class="w-full p-3 border rounded-md" />
        </div>
        <div>
          <label class="block text-sm font-medium mb-2">{{ $t('auth.password') }}</label>
          <input v-model="form.password" type="password" required class="w-full p-3 border rounded-md" />
        </div>

        <div v-if="error" class="text-red-500 text-sm">{{ error }}</div>
        <button type="submit" :disabled="loading" class="w-full bg-primary text-white p-3 rounded-md">
          {{ loading ? 'Logging in...' : $t('auth.login') }}
        </button>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useAuth } from '@/composables/useAuth'

const { signIn, loading } = useAuth()

const form = ref({
  email: '',
  password: ''
})

const error = ref('')

async function handleLogin() {
  if (!form.value.email || !form.value.password) {
    error.value = 'Please fill in all fields'
    return
  }

  try {
    error.value = ''

    const result = await signIn(form.value.email, form.value.password)

    if (result.error) {
      error.value = result.error.message || 'Invalid email or password'
    }
  } catch (err) {
    error.value = 'An unexpected error occurred'
  }
}
</script>