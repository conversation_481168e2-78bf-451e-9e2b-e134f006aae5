<template>
  <div>
    <h1 class="text-3xl font-bold mb-6">Instructor Dashboard</h1>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div class="bg-card p-6 rounded-lg border">
        <h3 class="font-semibold mb-2">My Courses</h3>
        <p class="text-2xl font-bold">{{ myCourses.length }}</p>
      </div>
      <div class="bg-card p-6 rounded-lg border">
        <h3 class="font-semibold mb-2">Total Students</h3>
        <p class="text-2xl font-bold">{{ totalStudents }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useCoursesStore } from '@/stores/courses'

const authStore = useAuthStore()
const coursesStore = useCoursesStore()

const myCourses = computed(() => 
  coursesStore.getCoursesByInstructor(authStore.user?.id || 0)
)

const totalStudents = computed(() => 
  myCourses.value.reduce((sum, course) => sum + course.studentCount, 0)
)
</script>