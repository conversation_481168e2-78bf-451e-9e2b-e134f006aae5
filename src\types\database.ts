export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: <PERSON><PERSON> | undefined }
  | Json[]

export type UserRole = 'student' | 'instructor' | 'admin'
export type CourseLevel = 'beginner' | 'intermediate' | 'advanced'
export type CourseStatus = 'draft' | 'published' | 'archived'
export type EnrollmentStatus = 'active' | 'completed' | 'cancelled'

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          bio: string | null
          role: UserRole
          website: string | null
          social_links: <PERSON><PERSON>
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          bio?: string | null
          role?: UserRole
          website?: string | null
          social_links?: <PERSON><PERSON>
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          bio?: string | null
          role?: UserRole
          website?: string | null
          social_links?: <PERSON><PERSON>
          created_at?: string
          updated_at?: string
        }
      }
      categories: {
        Row: {
          id: string
          name: string
          slug: string
          description: string | null
          image_url: string | null
          icon: string | null
          parent_id: string | null
          sort_order: number
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          slug: string
          description?: string | null
          image_url?: string | null
          icon?: string | null
          parent_id?: string | null
          sort_order?: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          slug?: string
          description?: string | null
          image_url?: string | null
          icon?: string | null
          parent_id?: string | null
          sort_order?: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      courses: {
        Row: {
          id: string
          title: string
          slug: string
          description: string | null
          short_description: string | null
          instructor_id: string | null
          category_id: string | null
          thumbnail_url: string | null
          preview_video_url: string | null
          level: CourseLevel
          status: CourseStatus
          price: number
          original_price: number | null
          currency: string
          duration_hours: number
          language: string
          requirements: string[] | null
          what_you_learn: string[] | null
          target_audience: string[] | null
          is_featured: boolean
          enrollment_count: number
          rating: number
          review_count: number
          published_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          slug: string
          description?: string | null
          short_description?: string | null
          instructor_id?: string | null
          category_id?: string | null
          thumbnail_url?: string | null
          preview_video_url?: string | null
          level?: CourseLevel
          status?: CourseStatus
          price?: number
          original_price?: number | null
          currency?: string
          duration_hours?: number
          language?: string
          requirements?: string[] | null
          what_you_learn?: string[] | null
          target_audience?: string[] | null
          is_featured?: boolean
          enrollment_count?: number
          rating?: number
          review_count?: number
          published_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          slug?: string
          description?: string | null
          short_description?: string | null
          instructor_id?: string | null
          category_id?: string | null
          thumbnail_url?: string | null
          preview_video_url?: string | null
          level?: CourseLevel
          status?: CourseStatus
          price?: number
          original_price?: number | null
          currency?: string
          duration_hours?: number
          language?: string
          requirements?: string[] | null
          what_you_learn?: string[] | null
          target_audience?: string[] | null
          is_featured?: boolean
          enrollment_count?: number
          rating?: number
          review_count?: number
          published_at?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      enrollments: {
        Row: {
          id: string
          student_id: string | null
          course_id: string | null
          status: EnrollmentStatus
          enrolled_at: string
          completed_at: string | null
          progress_percentage: number
          last_accessed_at: string
        }
        Insert: {
          id?: string
          student_id?: string | null
          course_id?: string | null
          status?: EnrollmentStatus
          enrolled_at?: string
          completed_at?: string | null
          progress_percentage?: number
          last_accessed_at?: string
        }
        Update: {
          id?: string
          student_id?: string | null
          course_id?: string | null
          status?: EnrollmentStatus
          enrolled_at?: string
          completed_at?: string | null
          progress_percentage?: number
          last_accessed_at?: string
        }
      }
      // Add more table types as needed...
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      user_role: UserRole
      course_level: CourseLevel
      course_status: CourseStatus
      enrollment_status: EnrollmentStatus
    }
  }
}
