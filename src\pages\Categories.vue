<template>
  <div class="min-h-screen bg-background">
    <div class="container py-12">
      <!-- Header Section -->
      <div class="text-center mb-12">
        <h1 class="text-4xl md:text-5xl font-bold mb-4 bg-gradient-primary bg-clip-text text-transparent">
          {{ $t('nav.categories') }}
        </h1>
        <p class="text-xl text-muted-foreground max-w-3xl mx-auto">
          Explore our diverse range of course categories and find the perfect learning path for your goals
        </p>
      </div>

      <!-- Search and Filter -->
      <div class="mb-8">
        <div class="relative max-w-md mx-auto">
          <Search class="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <input
            v-model="searchQuery"
            placeholder="Search categories..."
            class="w-full pl-10 h-12 px-3 py-1 text-sm shadow-sm transition-colors border border-input bg-background rounded-md focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"
          />
        </div>
      </div>

      <!-- Categories Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <div
          v-for="category in filteredCategories"
          :key="category.id"
          @click="navigateToCategory(category.slug)"
          class="group bg-card rounded-lg border shadow-sm hover:shadow-elevated transition-all duration-300 cursor-pointer overflow-hidden"
        >
          <div class="relative h-48 overflow-hidden">
            <img
              :src="category.image"
              :alt="category.name"
              class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
            />
            <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
            <div class="absolute bottom-4 left-4 text-white">
              <component :is="category.icon" class="h-8 w-8 mb-2" />
              <h3 class="text-xl font-bold">{{ category.name }}</h3>
            </div>
          </div>
          
          <div class="p-6">
            <p class="text-muted-foreground mb-4 line-clamp-2">
              {{ category.description }}
            </p>
            
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-4 text-sm text-muted-foreground">
                <span class="flex items-center gap-1">
                  <BookOpen class="h-4 w-4" />
                  {{ category.courseCount }} courses
                </span>
                <span class="flex items-center gap-1">
                  <Users class="h-4 w-4" />
                  {{ category.studentCount?.toLocaleString() }} students
                </span>
              </div>
              <ArrowRight class="h-4 w-4 text-primary group-hover:translate-x-1 transition-transform" />
            </div>
          </div>
        </div>
      </div>

      <!-- Popular Skills Section -->
      <div class="mt-16">
        <h2 class="text-3xl font-bold text-center mb-8">Popular Skills</h2>
        <div class="flex flex-wrap gap-3 justify-center">
          <span
            v-for="skill in popularSkills"
            :key="skill"
            class="px-4 py-2 bg-accent text-accent-foreground rounded-full text-sm font-medium hover:bg-primary hover:text-primary-foreground transition-colors cursor-pointer"
          >
            {{ skill }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { 
  Search, 
  BookOpen, 
  Users, 
  ArrowRight,
  Code,
  Palette,
  TrendingUp,
  Camera,
  Music,
  Briefcase,
  Heart,
  Zap
} from 'lucide-vue-next'

const router = useRouter()
const { t } = useI18n()

const searchQuery = ref('')

// Mock categories data
const categories = ref([
  {
    id: 1,
    name: 'Web Development',
    slug: 'web-development',
    description: 'Learn modern web technologies including HTML, CSS, JavaScript, React, Vue, and more.',
    image: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=400&h=300&fit=crop',
    icon: Code,
    courseCount: 245,
    studentCount: 125000
  },
  {
    id: 2,
    name: 'Design',
    slug: 'design',
    description: 'Master UI/UX design, graphic design, and digital art with industry-standard tools.',
    image: 'https://images.unsplash.com/photo-1558655146-9f40138edfeb?w=400&h=300&fit=crop',
    icon: Palette,
    courseCount: 189,
    studentCount: 89000
  },
  {
    id: 3,
    name: 'Business',
    slug: 'business',
    description: 'Develop essential business skills including marketing, management, and entrepreneurship.',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop',
    icon: TrendingUp,
    courseCount: 156,
    studentCount: 67000
  },
  {
    id: 4,
    name: 'Photography',
    slug: 'photography',
    description: 'Capture stunning photos and learn professional photography techniques.',
    image: 'https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=400&h=300&fit=crop',
    icon: Camera,
    courseCount: 98,
    studentCount: 45000
  },
  {
    id: 5,
    name: 'Music',
    slug: 'music',
    description: 'Learn instruments, music theory, production, and composition.',
    image: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=300&fit=crop',
    icon: Music,
    courseCount: 134,
    studentCount: 56000
  },
  {
    id: 6,
    name: 'Marketing',
    slug: 'marketing',
    description: 'Master digital marketing, social media, SEO, and brand strategy.',
    image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop',
    icon: Briefcase,
    courseCount: 167,
    studentCount: 78000
  },
  {
    id: 7,
    name: 'Health & Wellness',
    slug: 'health-wellness',
    description: 'Improve your physical and mental well-being with expert guidance.',
    image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
    icon: Heart,
    courseCount: 89,
    studentCount: 34000
  },
  {
    id: 8,
    name: 'Personal Development',
    slug: 'personal-development',
    description: 'Build confidence, productivity, and life skills for personal growth.',
    image: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=300&fit=crop',
    icon: Zap,
    courseCount: 112,
    studentCount: 52000
  }
])

const popularSkills = ref([
  'JavaScript', 'Python', 'React', 'Vue.js', 'Node.js', 'UI/UX Design',
  'Digital Marketing', 'Photography', 'Data Science', 'Machine Learning',
  'Graphic Design', 'WordPress', 'SEO', 'Social Media Marketing'
])

const filteredCategories = computed(() => {
  if (!searchQuery.value) return categories.value
  
  return categories.value.filter(category =>
    category.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    category.description.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

function navigateToCategory(slug: string) {
  router.push(`/courses?category=${slug}`)
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
