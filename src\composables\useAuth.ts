import { computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useRouter } from 'vue-router'

export function useAuth() {
  const authStore = useAuthStore()
  const router = useRouter()

  const user = computed(() => authStore.user)
  const profile = computed(() => authStore.profile)
  const isAuthenticated = computed(() => authStore.isAuthenticated)
  const isStudent = computed(() => authStore.isStudent)
  const isInstructor = computed(() => authStore.isInstructor)
  const isAdmin = computed(() => authStore.isAdmin)
  const loading = computed(() => authStore.loading)

  async function signUp(email: string, password: string, userData: { full_name: string; role: 'student' | 'instructor' }) {
    const result = await authStore.signUp(email, password, userData)
    
    if (result.data && !result.error) {
      // Redirect based on role
      if (userData.role === 'instructor') {
        router.push('/instructor')
      } else {
        router.push('/student')
      }
    }
    
    return result
  }

  async function signIn(email: string, password: string) {
    const result = await authStore.signIn(email, password)
    
    if (result.data && !result.error) {
      // Redirect based on role
      if (authStore.isInstructor) {
        router.push('/instructor')
      } else if (authStore.isStudent) {
        router.push('/student')
      } else {
        router.push('/')
      }
    }
    
    return result
  }

  async function signOut() {
    await authStore.signOut()
    router.push('/')
  }

  function requireAuth() {
    if (!isAuthenticated.value) {
      router.push('/auth/login')
      return false
    }
    return true
  }

  function requireRole(role: 'student' | 'instructor' | 'admin') {
    if (!requireAuth()) return false
    
    if (profile.value?.role !== role) {
      router.push('/')
      return false
    }
    return true
  }

  return {
    user,
    profile,
    isAuthenticated,
    isStudent,
    isInstructor,
    isAdmin,
    loading,
    signUp,
    signIn,
    signOut,
    requireAuth,
    requireRole
  }
}
