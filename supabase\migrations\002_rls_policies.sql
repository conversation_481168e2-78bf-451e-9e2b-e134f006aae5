-- Enable Row Level Security on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE courses ENABLE ROW LEVEL SECURITY;
ALTER TABLE course_modules ENABLE ROW LEVEL SECURITY;
ALTER TABLE lessons ENABLE ROW LEVEL SECURITY;
ALTER TABLE enrollments ENABLE ROW LEVEL SECURITY;
ALTER TABLE lesson_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE course_materials ENABLE ROW LEVEL SECURITY;

-- Profiles policies
CREATE POLICY "Public profiles are viewable by everyone" ON profiles
    FOR SELECT USING (true);

CREATE POLICY "Users can insert their own profile" ON profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON profiles
    FOR UPDATE USING (auth.uid() = id);

-- Categories policies (public read, admin write)
CREATE POLICY "Categories are viewable by everyone" ON categories
    FOR SELECT USING (is_active = true);

CREATE POLICY "Only admins can manage categories" ON categories
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
        )
    );

-- Courses policies
CREATE POLICY "Published courses are viewable by everyone" ON courses
    FOR SELECT USING (status = 'published');

CREATE POLICY "Instructors can view their own courses" ON courses
    FOR SELECT USING (instructor_id = auth.uid());

CREATE POLICY "Instructors can create courses" ON courses
    FOR INSERT WITH CHECK (
        instructor_id = auth.uid() AND
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role IN ('instructor', 'admin')
        )
    );

CREATE POLICY "Instructors can update their own courses" ON courses
    FOR UPDATE USING (instructor_id = auth.uid());

CREATE POLICY "Instructors can delete their own courses" ON courses
    FOR DELETE USING (instructor_id = auth.uid());

-- Course modules policies
CREATE POLICY "Modules are viewable for published courses" ON course_modules
    FOR SELECT USING (
        is_published = true AND
        EXISTS (
            SELECT 1 FROM courses 
            WHERE courses.id = course_modules.course_id 
            AND courses.status = 'published'
        )
    );

CREATE POLICY "Instructors can view their course modules" ON course_modules
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM courses 
            WHERE courses.id = course_modules.course_id 
            AND courses.instructor_id = auth.uid()
        )
    );

CREATE POLICY "Instructors can manage their course modules" ON course_modules
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM courses 
            WHERE courses.id = course_modules.course_id 
            AND courses.instructor_id = auth.uid()
        )
    );

-- Lessons policies
CREATE POLICY "Published lessons are viewable" ON lessons
    FOR SELECT USING (
        (is_published = true AND is_preview = true) OR
        EXISTS (
            SELECT 1 FROM course_modules cm
            JOIN courses c ON c.id = cm.course_id
            WHERE cm.id = lessons.module_id
            AND c.status = 'published'
            AND (
                is_preview = true OR
                EXISTS (
                    SELECT 1 FROM enrollments e
                    WHERE e.course_id = c.id
                    AND e.student_id = auth.uid()
                    AND e.status = 'active'
                )
            )
        )
    );

CREATE POLICY "Instructors can manage their lessons" ON lessons
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM course_modules cm
            JOIN courses c ON c.id = cm.course_id
            WHERE cm.id = lessons.module_id
            AND c.instructor_id = auth.uid()
        )
    );

-- Enrollments policies
CREATE POLICY "Students can view their own enrollments" ON enrollments
    FOR SELECT USING (student_id = auth.uid());

CREATE POLICY "Students can enroll in courses" ON enrollments
    FOR INSERT WITH CHECK (
        student_id = auth.uid() AND
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role IN ('student', 'admin')
        )
    );

CREATE POLICY "Students can update their enrollments" ON enrollments
    FOR UPDATE USING (student_id = auth.uid());

CREATE POLICY "Instructors can view enrollments for their courses" ON enrollments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM courses 
            WHERE courses.id = enrollments.course_id 
            AND courses.instructor_id = auth.uid()
        )
    );

-- Lesson progress policies
CREATE POLICY "Students can view their own progress" ON lesson_progress
    FOR SELECT USING (student_id = auth.uid());

CREATE POLICY "Students can update their own progress" ON lesson_progress
    FOR ALL USING (student_id = auth.uid());

CREATE POLICY "Instructors can view progress for their courses" ON lesson_progress
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM lessons l
            JOIN course_modules cm ON cm.id = l.module_id
            JOIN courses c ON c.id = cm.course_id
            WHERE l.id = lesson_progress.lesson_id
            AND c.instructor_id = auth.uid()
        )
    );

-- Reviews policies
CREATE POLICY "Public reviews are viewable by everyone" ON reviews
    FOR SELECT USING (is_public = true);

CREATE POLICY "Students can view their own reviews" ON reviews
    FOR SELECT USING (student_id = auth.uid());

CREATE POLICY "Students can create reviews for enrolled courses" ON reviews
    FOR INSERT WITH CHECK (
        student_id = auth.uid() AND
        EXISTS (
            SELECT 1 FROM enrollments 
            WHERE enrollments.course_id = reviews.course_id 
            AND enrollments.student_id = auth.uid()
            AND enrollments.status IN ('active', 'completed')
        )
    );

CREATE POLICY "Students can update their own reviews" ON reviews
    FOR UPDATE USING (student_id = auth.uid());

CREATE POLICY "Students can delete their own reviews" ON reviews
    FOR DELETE USING (student_id = auth.uid());

-- Course materials policies
CREATE POLICY "Materials are viewable for enrolled students" ON course_materials
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM enrollments e
            WHERE e.course_id = course_materials.course_id
            AND e.student_id = auth.uid()
            AND e.status = 'active'
        ) OR
        EXISTS (
            SELECT 1 FROM courses c
            WHERE c.id = course_materials.course_id
            AND c.instructor_id = auth.uid()
        )
    );

CREATE POLICY "Instructors can manage their course materials" ON course_materials
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM courses 
            WHERE courses.id = course_materials.course_id 
            AND courses.instructor_id = auth.uid()
        )
    );
