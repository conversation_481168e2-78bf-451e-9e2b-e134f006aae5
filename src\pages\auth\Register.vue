<template>
  <div class="bg-card rounded-lg shadow-lg p-8">
    <h1 class="text-2xl font-bold text-center mb-6">{{ $t('auth.register') }}</h1>
    <form @submit.prevent="handleRegister">
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium mb-2">Name</label>
          <input v-model="form.name" type="text" required class="w-full p-3 border rounded-md" />
        </div>
        <div>
          <label class="block text-sm font-medium mb-2">{{ $t('auth.email') }}</label>
          <input v-model="form.email" type="email" required class="w-full p-3 border rounded-md" />
        </div>
        <div>
          <label class="block text-sm font-medium mb-2">{{ $t('auth.password') }}</label>
          <input v-model="form.password" type="password" required class="w-full p-3 border rounded-md" />
        </div>
        <div>
          <label class="block text-sm font-medium mb-2">{{ $t('auth.role') }}</label>
          <select v-model="form.role" class="w-full p-3 border rounded-md">
            <option value="student">{{ $t('auth.student') }}</option>
            <option value="instructor">{{ $t('auth.instructor') }}</option>
          </select>
        </div>
        <button type="submit" :disabled="authStore.isLoading" class="w-full bg-primary text-white p-3 rounded-md">
          {{ authStore.isLoading ? 'Creating account...' : $t('auth.register') }}
        </button>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const form = ref({
  name: '',
  email: '',
  password: '',
  role: 'student' as 'student' | 'instructor'
})

async function handleRegister() {
  const success = await authStore.register(form.value.name, form.value.email, form.value.password, form.value.role)
  if (success) {
    router.push(form.value.role === 'student' ? '/student' : '/instructor')
  }
}
</script>