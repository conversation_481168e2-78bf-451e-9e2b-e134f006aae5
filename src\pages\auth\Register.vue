<template>
  <div class="bg-card rounded-lg shadow-lg p-8">
    <h1 class="text-2xl font-bold text-center mb-6">{{ $t('auth.register') }}</h1>

    <div v-if="error" class="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
      {{ error }}
    </div>

    <form @submit.prevent="handleRegister">
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium mb-2">Name</label>
          <input v-model="form.name" type="text" required class="w-full p-3 border rounded-md" />
        </div>
        <div>
          <label class="block text-sm font-medium mb-2">{{ $t('auth.email') }}</label>
          <input v-model="form.email" type="email" required class="w-full p-3 border rounded-md" />
        </div>
        <div>
          <label class="block text-sm font-medium mb-2">{{ $t('auth.password') }}</label>
          <input v-model="form.password" type="password" required class="w-full p-3 border rounded-md" />
        </div>
        <div>
          <label class="block text-sm font-medium mb-2">{{ $t('auth.role') }}</label>
          <select v-model="form.role" class="w-full p-3 border rounded-md">
            <option value="student">{{ $t('auth.student') }}</option>
            <option value="instructor">{{ $t('auth.instructor') }}</option>
          </select>
        </div>
        <button type="submit" :disabled="authStore.loading" class="w-full bg-primary text-white p-3 rounded-md">
          {{ authStore.loading ? 'Creating account...' : $t('auth.register') }}
        </button>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const form = ref({
  name: '',
  email: '',
  password: '',
  role: 'student' as 'student' | 'instructor'
})

const error = ref('')

async function handleRegister() {
  error.value = ''

  if (!form.value.name || !form.value.email || !form.value.password) {
    error.value = 'Please fill in all fields'
    return
  }

  console.log('Starting registration for:', form.value.email, 'as', form.value.role)

  const result = await authStore.signUp(form.value.email, form.value.password, {
    full_name: form.value.name,
    role: form.value.role
  })

  console.log('Registration result:', result)

  if (result.data && !result.error) {
    console.log('Registration successful, redirecting...')
    router.push(form.value.role === 'student' ? '/student' : '/instructor')
  } else if (result.error) {
    console.error('Registration error:', result.error)
    error.value = result.error.message || 'Registration failed. Please try again.'
  }
}
</script>