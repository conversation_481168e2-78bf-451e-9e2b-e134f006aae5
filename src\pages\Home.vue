<template>
  <div class="min-h-screen">
    <!-- Hero Section -->
    <section class="relative py-20 overflow-hidden">
      <div class="absolute inset-0 bg-gradient-hero opacity-90" />
      <div 
        class="absolute inset-0 bg-cover bg-center opacity-20"
        :style="{ backgroundImage: `url(${heroImage})` }"
      />
      
      <div class="container relative z-10 text-center text-white">
        <h1 class="text-5xl md:text-6xl font-bold mb-6 animate-fade-in">
          {{ $t('hero.title') }}
        </h1>
        <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto opacity-90 animate-slide-up">
          {{ $t('hero.subtitle') }}
        </p>
        
        <div class="flex flex-col sm:flex-row gap-4 max-w-2xl mx-auto mb-12 animate-scale-in">
          <div class="relative flex-1">
            <Search class="absolute left-4 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-400" />
            <input
              v-model="searchQuery"
              :placeholder="$t('hero.searchPlaceholder')"
              class="w-full pl-12 h-12 text-lg bg-white/95 backdrop-blur border-0 rounded-md px-3 text-gray-900"
            />
          </div>
          <button
            @click="searchCourses"
            class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 bg-white text-primary hover:bg-gray-50 h-12 px-8"
          >
            <Search class="mr-2 h-5 w-5" />
            {{ $t('hero.search') }}
          </button>
        </div>

        <div class="flex flex-wrap gap-4 justify-center">
          <div class="flex items-center gap-2 text-white/90">
            <Users class="h-5 w-5" />
            <span>2M+ {{ $t('hero.stats.students') }}</span>
          </div>
          <div class="flex items-center gap-2 text-white/90">
            <PlayCircle class="h-5 w-5" />
            <span>10k+ {{ $t('hero.stats.courses') }}</span>
          </div>
          <div class="flex items-center gap-2 text-white/90">
            <Award class="h-5 w-5" />
            <span>{{ $t('hero.stats.instructors') }}</span>
          </div>
        </div>
      </div>
    </section>

    <!-- Stats Section -->
    <section class="py-16 bg-muted/50">
      <div class="container">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          <div class="animate-fade-in">
            <div class="text-3xl font-bold text-primary mb-2">2M+</div>
            <div class="text-muted-foreground">Students Enrolled</div>
          </div>
          <div class="animate-fade-in">
            <div class="text-3xl font-bold text-primary mb-2">10k+</div>
            <div class="text-muted-foreground">Courses Available</div>
          </div>
          <div class="animate-fade-in">
            <div class="text-3xl font-bold text-primary mb-2">500+</div>
            <div class="text-muted-foreground">Expert Instructors</div>
          </div>
          <div class="animate-fade-in">
            <div class="text-3xl font-bold text-primary mb-2">95%</div>
            <div class="text-muted-foreground">Completion Rate</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Featured Courses -->
    <section class="py-16">
      <div class="container">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold mb-4">Featured Courses</h2>
          <p class="text-lg text-muted-foreground max-w-2xl mx-auto">
            Discover our most popular courses, carefully selected by our expert team
          </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <CourseCard
            v-for="course in featuredCourses"
            :key="course.id"
            v-bind="course"
          />
        </div>

        <div class="text-center">
          <router-link
            to="/courses"
            class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-10 px-8"
          >
            View All Courses
          </router-link>
        </div>
      </div>
    </section>

    <!-- Categories -->
    <section class="py-16 bg-muted/50">
      <div class="container">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold mb-4">Top Categories</h2>
          <p class="text-lg text-muted-foreground max-w-2xl mx-auto">
            Explore courses across multiple disciplines and find your passion
          </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div
            v-for="category in topCategories"
            :key="category.id"
            class="group cursor-pointer hover:shadow-elevated transition-all duration-300 bg-card rounded-lg border shadow-sm overflow-hidden"
          >
            <div class="relative overflow-hidden">
              <img
                :src="category.imageUrl"
                :alt="category.name"
                class="h-32 w-full object-cover transition-transform duration-300 group-hover:scale-110"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
              <div class="absolute bottom-4 left-4 text-white">
                <h3 class="font-semibold text-lg mb-1">{{ category.name }}</h3>
                <p class="text-sm opacity-90">{{ category.courseCount }} courses</p>
              </div>
            </div>
            <div class="p-4">
              <p class="text-sm text-muted-foreground">{{ category.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-gradient-primary">
      <div class="container text-center text-white">
        <h2 class="text-4xl font-bold mb-6">Ready to Start Learning?</h2>
        <p class="text-xl mb-8 max-w-2xl mx-auto opacity-90">
          Join millions of learners worldwide and advance your career with our expert-led courses
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <router-link
            to="/auth/register"
            class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 h-10 px-8"
          >
            Get Started Today
          </router-link>
          <router-link
            to="/courses"
            class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border text-white border-white hover:bg-white hover:text-primary h-10 px-8"
          >
            Browse Courses
          </router-link>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { Search, PlayCircle, Users, Award } from 'lucide-vue-next'
import CourseCard from '@/components/CourseCard.vue'
import { useCoursesStore } from '@/stores/courses'
import { mockCategories } from '@/data/mockData'
import heroImage from '@/assets/hero-learning.jpg'

const router = useRouter()
const coursesStore = useCoursesStore()

const searchQuery = ref('')

const featuredCourses = computed(() => coursesStore.featuredCourses)
const topCategories = computed(() => mockCategories.slice(0, 6))

function searchCourses() {
  if (searchQuery.value.trim()) {
    router.push(`/courses?search=${encodeURIComponent(searchQuery.value)}`)
  } else {
    router.push('/courses')
  }
}
</script>