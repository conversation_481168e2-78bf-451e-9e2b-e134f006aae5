<template>
  <div class="bg-card rounded-lg shadow-lg p-8">
    <h1 class="text-2xl font-bold text-center mb-6">Reset Password</h1>
    <form @submit.prevent="handleReset">
      <div class="mb-4">
        <label class="block text-sm font-medium mb-2">Email</label>
        <input v-model="email" type="email" required class="w-full p-3 border rounded-md" />
      </div>
      <button type="submit" class="w-full bg-primary text-white p-3 rounded-md">Send Reset Link</button>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const email = ref('')

function handleReset() {
  // Mock password reset
  alert('Password reset link sent!')
}
</script>