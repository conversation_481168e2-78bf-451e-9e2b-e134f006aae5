<template>
  <div class="py-8">
    <div class="container">
      <div class="mb-8">
        <h1 class="text-3xl font-bold mb-4">All Courses</h1>
        <p class="text-lg text-muted-foreground">
          Explore our comprehensive collection of courses
        </p>
      </div>

      <!-- Filters -->
      <div class="flex flex-col md:flex-row gap-4 mb-8">
        <div class="relative flex-1">
          <Search class="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <input
            v-model="searchQuery"
            placeholder="Search courses..."
            class="w-full pl-10 h-10 px-3 py-2 text-sm border border-input bg-background rounded-md"
          />
        </div>
        
        <select
          v-model="selectedCategory"
          class="h-10 px-3 py-2 text-sm border border-input bg-background rounded-md"
        >
          <option value="">All Categories</option>
          <option
            v-for="category in categories"
            :key="category.id"
            :value="category.name"
          >
            {{ category.name }}
          </option>
        </select>
        
        <select
          v-model="selectedLevel"
          class="h-10 px-3 py-2 text-sm border border-input bg-background rounded-md"
        >
          <option value="">All Levels</option>
          <option value="Beginner">Beginner</option>
          <option value="Intermediate">Intermediate</option>
          <option value="Advanced">Advanced</option>
        </select>
      </div>

      <!-- Results -->
      <div class="mb-4">
        <p class="text-sm text-muted-foreground">
          Showing {{ filteredCourses.length }} of {{ coursesStore.courses.length }} courses
        </p>
      </div>

      <!-- Course Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <CourseCard
          v-for="course in filteredCourses"
          :key="course.id"
          v-bind="course"
        />
      </div>

      <!-- No Results -->
      <div v-if="filteredCourses.length === 0" class="text-center py-12">
        <p class="text-lg text-muted-foreground">No courses found matching your criteria.</p>
        <button
          @click="clearFilters"
          class="mt-4 inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground shadow hover:bg-primary/90 h-10 px-4"
        >
          Clear Filters
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { Search } from 'lucide-vue-next'
import CourseCard from '@/components/CourseCard.vue'
import { useCoursesStore } from '@/stores/courses'
import { mockCategories } from '@/data/mockData'

const route = useRoute()
const coursesStore = useCoursesStore()

const searchQuery = ref('')
const selectedCategory = ref('')
const selectedLevel = ref('')

const categories = mockCategories

const filteredCourses = computed(() => {
  let filtered = coursesStore.courses

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(course =>
      course.title.toLowerCase().includes(query) ||
      course.description.toLowerCase().includes(query) ||
      course.instructor.name.toLowerCase().includes(query)
    )
  }

  if (selectedCategory.value) {
    filtered = filtered.filter(course => course.category === selectedCategory.value)
  }

  if (selectedLevel.value) {
    filtered = filtered.filter(course => course.level === selectedLevel.value)
  }

  return filtered
})

function clearFilters() {
  searchQuery.value = ''
  selectedCategory.value = ''
  selectedLevel.value = ''
}

onMounted(() => {
  // Get search query from URL if present
  if (route.query.search) {
    searchQuery.value = route.query.search as string
  }
})
</script>