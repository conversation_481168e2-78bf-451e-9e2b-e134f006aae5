import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// Layouts
import DefaultLayout from '@/layouts/DefaultLayout.vue'
import AuthLayout from '@/layouts/AuthLayout.vue'
import DashboardLayout from '@/layouts/DashboardLayout.vue'

// Pages
import Home from '@/pages/Home.vue'
import Courses from '@/pages/Courses.vue'
import Categories from '@/pages/Categories.vue'
import About from '@/pages/About.vue'
import CourseDetail from '@/pages/CourseDetail.vue'
import Login from '@/pages/auth/Login.vue'
import Register from '@/pages/auth/Register.vue'
import ForgotPassword from '@/pages/auth/ForgotPassword.vue'

// Student Dashboard
import StudentDashboard from '@/pages/dashboard/StudentDashboard.vue'
import MyEnrollments from '@/pages/dashboard/MyEnrollments.vue'

// Instructor Dashboard
import InstructorDashboard from '@/pages/dashboard/InstructorDashboard.vue'
import MyCourses from '@/pages/dashboard/MyCourses.vue'
import CreateCourse from '@/pages/dashboard/CreateCourse.vue'
import EditCourse from '@/pages/dashboard/EditCourse.vue'
import CourseFeedback from '@/pages/dashboard/CourseFeedback.vue'

import NotFound from '@/pages/NotFound.vue'

const routes = [
  // Public routes
  {
    path: '/',
    component: DefaultLayout,
    children: [
      { path: '', name: 'Home', component: Home },
      { path: 'courses', name: 'Courses', component: Courses },
      { path: 'categories', name: 'Categories', component: Categories },
      { path: 'about', name: 'About', component: About },
      { path: 'course/:id', name: 'CourseDetail', component: CourseDetail },
    ]
  },
  
  // Auth routes
  {
    path: '/auth',
    component: AuthLayout,
    children: [
      { path: 'login', name: 'Login', component: Login },
      { path: 'register', name: 'Register', component: Register },
      { path: 'forgot-password', name: 'ForgotPassword', component: ForgotPassword },
    ]
  },

  // Student Dashboard
  {
    path: '/student',
    component: DashboardLayout,
    meta: { requiresAuth: true, role: 'student' },
    children: [
      { path: '', name: 'StudentDashboard', component: StudentDashboard },
      { path: 'enrollments', name: 'MyEnrollments', component: MyEnrollments },
    ]
  },

  // Instructor Dashboard
  {
    path: '/instructor',
    component: DashboardLayout,
    meta: { requiresAuth: true, role: 'instructor' },
    children: [
      { path: '', name: 'InstructorDashboard', component: InstructorDashboard },
      { path: 'courses', name: 'MyCourses', component: MyCourses },
      { path: 'courses/create', name: 'CreateCourse', component: CreateCourse },
      { path: 'courses/edit/:id', name: 'EditCourse', component: EditCourse },
      { path: 'courses/:id/feedback', name: 'CourseFeedback', component: CourseFeedback },
    ]
  },

  // 404
  { path: '/:pathMatch(.*)*', name: 'NotFound', component: NotFound }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// Navigation guards
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next('/auth/login')
    return
  }
  
  if (to.meta.role && authStore.user?.role !== to.meta.role) {
    next('/')
    return
  }
  
  next()
})

export default router