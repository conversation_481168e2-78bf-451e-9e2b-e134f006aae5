export interface Course {
  id: number;
  title: string;
  description: string;
  instructor: {
    id: number;
    name: string;
    avatar: string;
    bio: string;
  };
  rating: number;
  reviewCount: number;
  price: number;
  originalPrice?: number;
  duration: string;
  studentCount: number;
  imageUrl: string;
  category: string;
  level: "Beginner" | "Intermediate" | "Advanced";
  language: string;
  lastUpdated: string;
  lessons: Lesson[];
  reviews: Review[];
  syllabus: SyllabusItem[];
}

export interface Lesson {
  id: number;
  title: string;
  duration: string;
  type: "video" | "article" | "quiz";
  isCompleted?: boolean;
  isFree?: boolean;
}

export interface Review {
  id: number;
  user: {
    name: string;
    avatar: string;
  };
  rating: number;
  comment: string;
  date: string;
  helpful: number;
}

export interface SyllabusItem {
  id: number;
  title: string;
  lessons: Lesson[];
}

export interface Category {
  id: number;
  name: string;
  description: string;
  courseCount: number;
  imageUrl: string;
}

export interface User {
  id: number;
  name: string;
  email: string;
  avatar: string;
  role: "student" | "instructor";
  enrolledCourses?: number[];
  createdCourses?: number[];
}