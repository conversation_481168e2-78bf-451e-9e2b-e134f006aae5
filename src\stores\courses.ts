import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Course, Review } from '@/types/course'
import { mockCourses } from '@/data/mockData'

export const useCoursesStore = defineStore('courses', () => {
  const courses = ref<Course[]>([...mockCourses])
  const isLoading = ref(false)

  const featuredCourses = computed(() => courses.value.slice(0, 4))
  const popularCourses = computed(() => 
    courses.value.sort((a, b) => b.studentCount - a.studentCount).slice(0, 8)
  )

  // Get courses by instructor
  function getCoursesByInstructor(instructorId: number) {
    return courses.value.filter(course => course.instructor.id === instructorId)
  }

  // Get course by ID
  function getCourseById(id: number) {
    return courses.value.find(course => course.id === id)
  }

  // Create new course (mock)
  async function createCourse(courseData: Partial<Course>) {
    isLoading.value = true
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const newCourse: Course = {
      id: Date.now(),
      title: courseData.title || '',
      description: courseData.description || '',
      instructor: courseData.instructor!,
      rating: 0,
      reviewCount: 0,
      price: courseData.price || 0,
      originalPrice: courseData.originalPrice,
      duration: courseData.duration || '0 hours',
      studentCount: 0,
      imageUrl: courseData.imageUrl || 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400&h=300&fit=crop',
      category: courseData.category || 'General',
      level: courseData.level || 'Beginner',
      language: courseData.language || 'English',
      lastUpdated: new Date().toLocaleDateString('en-US', { month: 'long', year: 'numeric' }),
      lessons: courseData.lessons || [],
      reviews: [],
      syllabus: courseData.syllabus || []
    }
    
    courses.value.push(newCourse)
    isLoading.value = false
    
    return newCourse
  }

  // Update course
  async function updateCourse(id: number, updates: Partial<Course>) {
    isLoading.value = true
    
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const index = courses.value.findIndex(course => course.id === id)
    if (index !== -1) {
      courses.value[index] = { ...courses.value[index], ...updates }
    }
    
    isLoading.value = false
  }

  // Delete course
  async function deleteCourse(id: number) {
    isLoading.value = true
    
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const index = courses.value.findIndex(course => course.id === id)
    if (index !== -1) {
      courses.value.splice(index, 1)
    }
    
    isLoading.value = false
  }

  // Add review to course
  async function addReview(courseId: number, review: Omit<Review, 'id'>) {
    const course = getCourseById(courseId)
    if (!course) return
    
    const newReview: Review = {
      id: Date.now(),
      ...review,
      date: new Date().toISOString().split('T')[0]
    }
    
    course.reviews.push(newReview)
    course.reviewCount = course.reviews.length
    
    // Recalculate rating
    const totalRating = course.reviews.reduce((sum, r) => sum + r.rating, 0)
    course.rating = Math.round((totalRating / course.reviews.length) * 10) / 10
  }

  // Enroll in course (mock)
  async function enrollInCourse(courseId: number) {
    const course = getCourseById(courseId)
    if (course) {
      course.studentCount += 1
      return true
    }
    return false
  }

  return {
    courses,
    isLoading,
    featuredCourses,
    popularCourses,
    getCoursesByInstructor,
    getCourseById,
    createCourse,
    updateCourse,
    deleteCourse,
    addReview,
    enrollInCourse
  }
})