<template>
  <div v-if="course" class="py-8">
    <div class="container">
      <!-- Course Header -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
        <div class="lg:col-span-2">
          <div class="mb-4">
            <span class="text-sm text-primary font-medium">{{ course.category }}</span>
          </div>
          
          <h1 class="text-4xl font-bold mb-4">{{ course.title }}</h1>
          
          <p class="text-lg text-muted-foreground mb-6">{{ course.description }}</p>
          
          <div class="flex items-center gap-4 mb-6">
            <div class="flex items-center gap-1">
              <Star
                v-for="i in 5"
                :key="i"
                class="h-5 w-5"
                :class="i <= Math.floor(course.rating) ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'"
              />
              <span class="font-medium ml-2">{{ course.rating }}</span>
              <span class="text-muted-foreground">({{ course.reviewCount }} reviews)</span>
            </div>
          </div>
          
          <div class="flex items-center gap-4 text-sm text-muted-foreground">
            <span>{{ course.studentCount.toLocaleString() }} students</span>
            <span>{{ course.duration }}</span>
            <span>{{ course.level }}</span>
            <span>Last updated {{ course.lastUpdated }}</span>
          </div>
        </div>
        
        <!-- Course Sidebar -->
        <div class="lg:col-span-1">
          <div class="bg-card border rounded-lg p-6 sticky top-24">
            <img
              :src="course.imageUrl"
              :alt="course.title"
              class="w-full h-48 object-cover rounded-lg mb-4"
            />
            
            <div class="flex items-center gap-2 mb-6">
              <span class="text-3xl font-bold">${{ course.price }}</span>
              <span v-if="course.originalPrice" class="text-lg text-muted-foreground line-through">
                ${{ course.originalPrice }}
              </span>
            </div>
            
            <button
              v-if="!isEnrolled"
              @click="enrollInCourse"
              :disabled="isEnrolling"
              class="w-full mb-4 inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground shadow hover:bg-primary/90 h-11 px-8"
            >
              {{ isEnrolling ? 'Enrolling...' : 'Enroll Now' }}
            </button>
            
            <div v-else class="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
              <p class="text-green-800 text-sm font-medium">✓ You are enrolled in this course</p>
            </div>
            
            <!-- Instructor Info -->
            <div class="border-t pt-4">
              <h3 class="font-semibold mb-3">Instructor</h3>
              <div class="flex items-center gap-3">
                <img
                  :src="course.instructor.avatar"
                  :alt="course.instructor.name"
                  class="h-12 w-12 rounded-full"
                />
                <div>
                  <p class="font-medium">{{ course.instructor.name }}</p>
                  <p class="text-sm text-muted-foreground">{{ course.instructor.bio }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Course Content Tabs -->
      <div class="border-b mb-8">
        <nav class="flex space-x-8">
          <button
            v-for="tab in tabs"
            :key="tab.id"
            @click="activeTab = tab.id"
            class="py-2 px-1 border-b-2 font-medium text-sm"
            :class="activeTab === tab.id 
              ? 'border-primary text-primary' 
              : 'border-transparent text-muted-foreground hover:text-foreground'"
          >
            {{ tab.label }}
          </button>
        </nav>
      </div>
      
      <!-- Tab Content -->
      <div v-if="activeTab === 'overview'">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h2 class="text-2xl font-bold mb-4">What you'll learn</h2>
            <ul class="space-y-2">
              <li v-for="i in 5" :key="i" class="flex items-start gap-2">
                <CheckCircle class="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <span>Learn modern web development techniques and best practices</span>
              </li>
            </ul>
          </div>
          
          <div>
            <h2 class="text-2xl font-bold mb-4">Course includes</h2>
            <ul class="space-y-2">
              <li class="flex items-center gap-2">
                <PlayCircle class="h-5 w-5 text-muted-foreground" />
                <span>{{ course.duration }} on-demand video</span>
              </li>
              <li class="flex items-center gap-2">
                <FileText class="h-5 w-5 text-muted-foreground" />
                <span>Downloadable resources</span>
              </li>
              <li class="flex items-center gap-2">
                <Award class="h-5 w-5 text-muted-foreground" />
                <span>Certificate of completion</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
      
      <div v-else-if="activeTab === 'curriculum'">
        <h2 class="text-2xl font-bold mb-6">Course curriculum</h2>
        <div class="space-y-4">
          <div v-for="section in course.syllabus" :key="section.id" class="border rounded-lg">
            <div class="p-4 bg-muted/50">
              <h3 class="font-semibold">{{ section.title }}</h3>
              <p class="text-sm text-muted-foreground">{{ section.lessons.length }} lessons</p>
            </div>
            <div class="divide-y">
              <div
                v-for="lesson in section.lessons"
                :key="lesson.id"
                class="p-4 flex items-center justify-between"
              >
                <div class="flex items-center gap-3">
                  <PlayCircle
                    v-if="lesson.type === 'video'"
                    class="h-5 w-5 text-muted-foreground"
                  />
                  <FileText
                    v-else-if="lesson.type === 'article'"
                    class="h-5 w-5 text-muted-foreground"
                  />
                  <HelpCircle
                    v-else
                    class="h-5 w-5 text-muted-foreground"
                  />
                  <span>{{ lesson.title }}</span>
                  <span v-if="lesson.isFree" class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                    Free
                  </span>
                </div>
                <span class="text-sm text-muted-foreground">{{ lesson.duration }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div v-else-if="activeTab === 'reviews'">
        <div class="mb-8">
          <h2 class="text-2xl font-bold mb-6">Student reviews</h2>
          
          <!-- Add Review Form (if enrolled) -->
          <div v-if="isEnrolled && authStore.isAuthenticated" class="mb-8 p-6 border rounded-lg">
            <h3 class="font-semibold mb-4">Leave a review</h3>
            <form @submit.prevent="submitReview">
              <div class="mb-4">
                <label class="block text-sm font-medium mb-2">Rating</label>
                <div class="flex gap-1">
                  <button
                    v-for="i in 5"
                    :key="i"
                    type="button"
                    @click="newReview.rating = i"
                    class="focus:outline-none"
                  >
                    <Star
                      class="h-6 w-6"
                      :class="i <= newReview.rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'"
                    />
                  </button>
                </div>
              </div>
              
              <div class="mb-4">
                <label class="block text-sm font-medium mb-2">Comment</label>
                <textarea
                  v-model="newReview.comment"
                  rows="3"
                  class="w-full p-3 border border-input rounded-md"
                  placeholder="Share your thoughts about this course..."
                ></textarea>
              </div>
              
              <button
                type="submit"
                :disabled="!newReview.rating || !newReview.comment"
                class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground shadow hover:bg-primary/90 h-10 px-4"
              >
                Submit Review
              </button>
            </form>
          </div>
          
          <!-- Reviews List -->
          <div class="space-y-6">
            <div
              v-for="review in course.reviews"
              :key="review.id"
              class="p-6 border rounded-lg"
            >
              <div class="flex items-start gap-4">
                <img
                  :src="review.user.avatar"
                  :alt="review.user.name"
                  class="h-10 w-10 rounded-full"
                />
                <div class="flex-1">
                  <div class="flex items-center gap-2 mb-2">
                    <span class="font-medium">{{ review.user.name }}</span>
                    <div class="flex">
                      <Star
                        v-for="i in 5"
                        :key="i"
                        class="h-4 w-4"
                        :class="i <= review.rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'"
                      />
                    </div>
                    <span class="text-sm text-muted-foreground">{{ review.date }}</span>
                  </div>
                  <p>{{ review.comment }}</p>
                  <div class="mt-3 flex items-center gap-4 text-sm text-muted-foreground">
                    <button class="hover:text-foreground">
                      👍 Helpful ({{ review.helpful }})
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <div v-else class="py-8">
    <div class="container">
      <p class="text-center text-lg">Course not found</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { 
  Star, 
  CheckCircle, 
  PlayCircle, 
  FileText, 
  Award, 
  HelpCircle 
} from 'lucide-vue-next'
import { useCoursesStore } from '@/stores/courses'
import { useAuthStore } from '@/stores/auth'

const route = useRoute()
const coursesStore = useCoursesStore()
const authStore = useAuthStore()

const activeTab = ref('overview')
const isEnrolling = ref(false)
const newReview = ref({
  rating: 0,
  comment: ''
})

const tabs = [
  { id: 'overview', label: 'Overview' },
  { id: 'curriculum', label: 'Curriculum' },
  { id: 'reviews', label: 'Reviews' }
]

const course = computed(() => {
  const id = Number(route.params.id)
  return coursesStore.getCourseById(id)
})

const isEnrolled = computed(() => {
  if (!authStore.user || !course.value) return false
  return authStore.user.enrolledCourses?.includes(course.value.id) || false
})

async function enrollInCourse() {
  if (!course.value) return
  
  isEnrolling.value = true
  await coursesStore.enrollInCourse(course.value.id)
  
  // Update user enrolled courses
  if (authStore.user) {
    if (!authStore.user.enrolledCourses) {
      authStore.user.enrolledCourses = []
    }
    authStore.user.enrolledCourses.push(course.value.id)
  }
  
  isEnrolling.value = false
}

async function submitReview() {
  if (!course.value || !authStore.user) return
  
  await coursesStore.addReview(course.value.id, {
    user: {
      name: authStore.user.name,
      avatar: authStore.user.avatar
    },
    rating: newReview.value.rating,
    comment: newReview.value.comment,
    helpful: 0
  })
  
  // Reset form
  newReview.value = {
    rating: 0,
    comment: ''
  }
}

onMounted(() => {
  authStore.initAuth()
})
</script>