<template>
  <div>
    <h1 class="text-3xl font-bold mb-6">Create New Course</h1>
    <form @submit.prevent="handleSubmit" class="space-y-6">
      <div>
        <label class="block text-sm font-medium mb-2">Course Title</label>
        <input v-model="form.title" type="text" required class="w-full p-3 border rounded-md" />
      </div>
      <div>
        <label class="block text-sm font-medium mb-2">Description</label>
        <textarea v-model="form.description" rows="4" required class="w-full p-3 border rounded-md"></textarea>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium mb-2">Price ($)</label>
          <input v-model.number="form.price" type="number" required class="w-full p-3 border rounded-md" />
        </div>
        <div>
          <label class="block text-sm font-medium mb-2">Category</label>
          <select v-model="form.category" required class="w-full p-3 border rounded-md">
            <option value="">Select Category</option>
            <option value="Web Development">Web Development</option>
            <option value="Data Science">Data Science</option>
            <option value="Marketing">Marketing</option>
          </select>
        </div>
      </div>
      <button type="submit" :disabled="coursesStore.isLoading" class="bg-primary text-white px-6 py-3 rounded-md">
        {{ coursesStore.isLoading ? 'Creating...' : 'Create Course' }}
      </button>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useCoursesStore } from '@/stores/courses'

const router = useRouter()
const authStore = useAuthStore()
const coursesStore = useCoursesStore()

const form = ref({
  title: '',
  description: '',
  price: 0,
  category: '',
  level: 'Beginner' as 'Beginner' | 'Intermediate' | 'Advanced'
})

async function handleSubmit() {
  if (!authStore.user) return
  
  await coursesStore.createCourse({
    ...form.value,
    instructor: {
      id: authStore.user.id,
      name: authStore.user.name,
      avatar: authStore.user.avatar,
      bio: 'Course instructor'
    },
    lessons: [],
    syllabus: []
  })
  
  router.push('/instructor/courses')
}
</script>