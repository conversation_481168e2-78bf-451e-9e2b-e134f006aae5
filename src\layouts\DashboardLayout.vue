<template>
  <div class="min-h-screen bg-muted/50">
    <Header />
    <div class="container py-8">
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- Sidebar -->
        <div class="lg:col-span-1">
          <nav class="space-y-2">
            <template v-if="authStore.isStudent">
              <router-link 
                to="/student" 
                class="block px-4 py-2 rounded-lg hover:bg-primary/10 transition-colors"
                :class="{ 'bg-primary/20': $route.name === 'StudentDashboard' }"
              >
                {{ $t('nav.dashboard') }}
              </router-link>
              <router-link 
                to="/student/enrollments" 
                class="block px-4 py-2 rounded-lg hover:bg-primary/10 transition-colors"
                :class="{ 'bg-primary/20': $route.name === 'MyEnrollments' }"
              >
                {{ $t('nav.myCourses') }}
              </router-link>
            </template>
            
            <template v-if="authStore.isInstructor">
              <router-link 
                to="/instructor" 
                class="block px-4 py-2 rounded-lg hover:bg-primary/10 transition-colors"
                :class="{ 'bg-primary/20': $route.name === 'InstructorDashboard' }"
              >
                {{ $t('nav.dashboard') }}
              </router-link>
              <router-link 
                to="/instructor/courses" 
                class="block px-4 py-2 rounded-lg hover:bg-primary/10 transition-colors"
                :class="{ 'bg-primary/20': $route.name === 'MyCourses' }"
              >
                {{ $t('nav.myCourses') }}
              </router-link>
              <router-link 
                to="/instructor/courses/create" 
                class="block px-4 py-2 rounded-lg hover:bg-primary/10 transition-colors"
                :class="{ 'bg-primary/20': $route.name === 'CreateCourse' }"
              >
                {{ $t('nav.createCourse') }}
              </router-link>
            </template>
          </nav>
        </div>
        
        <!-- Main Content -->
        <div class="lg:col-span-3">
          <router-view />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from '@/stores/auth'
import Header from '@/components/Header.vue'

const authStore = useAuthStore()
</script>