import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { supabase } from '@/lib/supabase'
import type { User, Session } from '@supabase/supabase-js'
import type { Database } from '@/types/database'

type Profile = Database['public']['Tables']['profiles']['Row']

export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const profile = ref<Profile | null>(null)
  const session = ref<Session | null>(null)
  const loading = ref(true)

  const isAuthenticated = computed(() => !!user.value)
  const isStudent = computed(() => profile.value?.role === 'student')
  const isInstructor = computed(() => profile.value?.role === 'instructor')
  const isAdmin = computed(() => profile.value?.role === 'admin')

  // Initialize auth state
  async function initialize() {
    try {
      loading.value = true

      // Get initial session
      const { data: { session: initialSession } } = await supabase.auth.getSession()

      if (initialSession) {
        await setSession(initialSession)
      }

      // Listen for auth changes
      supabase.auth.onAuthStateChange(async (event, newSession) => {
        if (event === 'SIGNED_IN' && newSession) {
          await setSession(newSession)
        } else if (event === 'SIGNED_OUT') {
          clearSession()
        }
      })
    } catch (error) {
      console.error('Auth initialization error:', error)
    } finally {
      loading.value = false
    }
  }

  async function setSession(newSession: Session) {
    session.value = newSession
    user.value = newSession.user

    // Fetch user profile
    if (newSession.user) {
      await fetchProfile(newSession.user.id)
    }
  }

  function clearSession() {
    session.value = null
    user.value = null
    profile.value = null
  }

  async function fetchProfile(userId: string) {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()

      if (error) throw error
      profile.value = data
    } catch (error) {
      console.error('Error fetching profile:', error)
    }
  }

  async function signUp(email: string, password: string, userData: { full_name: string; role: 'student' | 'instructor' }) {
    try {
      loading.value = true

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: userData
        }
      })

      if (error) throw error

      // Wait a moment for the database trigger to create the profile
      if (data.user) {
        // Check if profile was created by trigger, if not create it manually
        await new Promise(resolve => setTimeout(resolve, 1000))

        const { data: existingProfile } = await supabase
          .from('profiles')
          .select('id')
          .eq('id', data.user.id)
          .single()

        if (!existingProfile) {
          // Profile wasn't created by trigger, create it manually
          const { error: profileError } = await supabase
            .from('profiles')
            .insert({
              id: data.user.id,
              email: data.user.email!,
              full_name: userData.full_name,
              role: userData.role
            })

          if (profileError) {
            console.error('Profile creation error:', profileError)
            // Don't throw here as the user account was created successfully
          }
        }
      }

      return { data, error: null }
    } catch (error) {
      console.error('Sign up error:', error)
      return { data: null, error }
    } finally {
      loading.value = false
    }
  }

  async function signIn(email: string, password: string) {
    try {
      loading.value = true

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      console.error('Sign in error:', error)
      return { data: null, error }
    } finally {
      loading.value = false
    }
  }

  async function signOut() {
    try {
      loading.value = true
      const { error } = await supabase.auth.signOut()
      if (error) throw error
    } catch (error) {
      console.error('Sign out error:', error)
    } finally {
      loading.value = false
    }
  }

  async function updateProfile(updates: Partial<Profile>) {
    try {
      if (!user.value) throw new Error('No user logged in')

      const { data, error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', user.value.id)
        .select()
        .single()

      if (error) throw error
      profile.value = data
      return { data, error: null }
    } catch (error) {
      console.error('Update profile error:', error)
      return { data: null, error }
    }
  }

  return {
    user,
    profile,
    session,
    loading,
    isAuthenticated,
    isStudent,
    isInstructor,
    isAdmin,
    initialize,
    signUp,
    signIn,
    signOut,
    updateProfile,
    fetchProfile
  }
})