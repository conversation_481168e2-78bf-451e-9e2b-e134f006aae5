import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { supabase } from '@/lib/supabase'
import type { User, Session } from '@supabase/supabase-js'
import type { Database } from '@/types/database'

type Profile = Database['public']['Tables']['profiles']['Row']

export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const profile = ref<Profile | null>(null)
  const session = ref<Session | null>(null)
  const loading = ref(true)

  const isAuthenticated = computed(() => !!user.value)
  const isStudent = computed(() => profile.value?.role === 'student')
  const isInstructor = computed(() => profile.value?.role === 'instructor')
  const isAdmin = computed(() => profile.value?.role === 'admin')

  // Initialize auth state
  async function initialize() {
    try {
      loading.value = true

      // Get initial session
      const { data: { session: initialSession } } = await supabase.auth.getSession()

      if (initialSession) {
        await setSession(initialSession)
      }

      // Listen for auth changes
      supabase.auth.onAuthStateChange(async (event, newSession) => {
        if (event === 'SIGNED_IN' && newSession) {
          await setSession(newSession)
        } else if (event === 'SIGNED_OUT') {
          clearSession()
        }
      })
    } catch (error) {
      console.error('Auth initialization error:', error)
    } finally {
      loading.value = false
    }
  }

  async function setSession(newSession: Session) {
    session.value = newSession
    user.value = newSession.user

    // Fetch user profile
    if (newSession.user) {
      await fetchProfile(newSession.user.id)
    }
  }

  function clearSession() {
    session.value = null
    user.value = null
    profile.value = null
  }

  async function fetchProfile(userId: string) {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()

      if (error) throw error
      profile.value = data
    } catch (error) {
      console.error('Error fetching profile:', error)
    }
  }

  async function signUp(email: string, password: string, userData: { full_name: string; role: 'student' | 'instructor' }) {
    try {
      loading.value = true

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: userData
        }
      })

      if (error) throw error

      // Create profile
      if (data.user) {
        const { error: profileError } = await supabase
          .from('profiles')
          .insert({
            id: data.user.id,
            email: data.user.email!,
            full_name: userData.full_name,
            role: userData.role
          })

        if (profileError) throw profileError
      }

      return { data, error: null }
    } catch (error) {
      console.error('Sign up error:', error)
      return { data: null, error }
    } finally {
      loading.value = false
    }
  }

  async function signIn(email: string, password: string) {
    try {
      loading.value = true

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      console.error('Sign in error:', error)
      return { data: null, error }
    } finally {
      loading.value = false
    }
  }

  async function signOut() {
    try {
      loading.value = true
      const { error } = await supabase.auth.signOut()
      if (error) throw error
    } catch (error) {
      console.error('Sign out error:', error)
    } finally {
      loading.value = false
    }
  }

  async function updateProfile(updates: Partial<Profile>) {
    try {
      if (!user.value) throw new Error('No user logged in')

      const { data, error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', user.value.id)
        .select()
        .single()

      if (error) throw error
      profile.value = data
      return { data, error: null }
    } catch (error) {
      console.error('Update profile error:', error)
      return { data: null, error }
    }
  }

  return {
    user,
    profile,
    session,
    loading,
    isAuthenticated,
    isStudent,
    isInstructor,
    isAdmin,
    initialize,
    signUp,
    signIn,
    signOut,
    updateProfile,
    fetchProfile
  }
})
      createdCourses: role === 'instructor' ? [1] : []
    }
    
    // Store in localStorage for persistence
    localStorage.setItem('auth_user', JSON.stringify(user.value))
    
    isLoading.value = false
    return true
  }

  // Mock register function
  async function register(name: string, email: string, password: string, role: 'student' | 'instructor') {
    isLoading.value = true
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    user.value = {
      id: Math.floor(Math.random() * 1000),
      name,
      email,
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',
      role,
      enrolledCourses: [],
      createdCourses: []
    }
    
    localStorage.setItem('auth_user', JSON.stringify(user.value))
    
    isLoading.value = false
    return true
  }

  function logout() {
    user.value = null
    localStorage.removeItem('auth_user')
  }

  function initAuth() {
    const stored = localStorage.getItem('auth_user')
    if (stored) {
      user.value = JSON.parse(stored)
    }
  }

  return {
    user,
    isLoading,
    isAuthenticated,
    isStudent,
    isInstructor,
    login,
    register,
    logout,
    initAuth
  }
})