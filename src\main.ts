import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createI18n } from 'vue-i18n'
import App from './App.vue'
import router from './router'
import './index.css'

// i18n setup
const messages = {
  en: {
    nav: {
      courses: 'Courses',
      categories: 'Categories', 
      about: 'About',
      dashboard: 'Dashboard',
      myCourses: 'My Courses',
      createCourse: 'Create Course',
      settings: 'Settings',
      login: 'Log In',
      signup: 'Sign Up',
      logout: 'Log Out'
    },
    hero: {
      title: 'Learn Without Limits',
      subtitle: 'Discover thousands of courses from expert instructors and transform your career today',
      searchPlaceholder: 'What do you want to learn?',
      search: 'Search',
      stats: {
        students: 'Students',
        courses: 'Courses',
        instructors: 'Expert Instructors'
      }
    },
    auth: {
      login: 'Login',
      register: 'Register',
      email: 'Email',
      password: 'Password',
      confirmPassword: 'Confirm Password',
      role: 'I want to',
      student: '<PERSON><PERSON> (Student)',
      instructor: 'Teach (Instructor)'
    }
  },
  am: {
    nav: {
      courses: 'ኮርሶች',
      categories: 'ምድቦች',
      about: 'ስለእኛ',
      dashboard: 'ዳሽቦርድ',
      myCourses: 'የእኔ ኮርሶች',
      createCourse: 'ኮርስ ፍጠር',
      settings: 'ቅንብሮች',
      login: 'ግባ',
      signup: 'ተመዝገብ',
      logout: 'ውጣ'
    },
    hero: {
      title: 'ያለ ገደብ ተማር',
      subtitle: 'ከባለሙያ አስተማሪዎች ሺዎች ኮርሶችን አግኝ እና ዛሬ ስራህን ለውጥ',
      searchPlaceholder: 'ምን መማር ትፈልጋለህ?',
      search: 'ፈልግ',
      stats: {
        students: 'ተማሪዎች',
        courses: 'ኮርሶች',
        instructors: 'ባለሙያ አስተማሪዎች'
      }
    },
    auth: {
      login: 'ግባ',
      register: 'ተመዝገብ',
      email: 'ኢሜይል',
      password: 'የይለፍ ቃል',
      confirmPassword: 'የይለፍ ቃል አረጋግጥ',
      role: 'እፈልጋለሁ',
      student: 'ለመማር (ተማሪ)',
      instructor: 'ለማስተማር (አስተማሪ)'
    }
  }
}

const i18n = createI18n({
  legacy: false,
  locale: 'en',
  fallbackLocale: 'en',
  messages,
})

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)
app.use(i18n)

// Initialize auth store
import { useAuthStore } from '@/stores/auth'
const authStore = useAuthStore()
authStore.initialize()

app.mount('#app')