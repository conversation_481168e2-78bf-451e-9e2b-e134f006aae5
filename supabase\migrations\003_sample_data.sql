-- Insert sample categories
INSERT INTO categories (name, slug, description, image_url, icon, sort_order) VALUES
('Web Development', 'web-development', 'Learn modern web technologies including HTML, CSS, JavaScript, React, Vue, and more.', 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=400&h=300&fit=crop', 'Code', 1),
('Design', 'design', 'Master UI/UX design, graphic design, and digital art with industry-standard tools.', 'https://images.unsplash.com/photo-1558655146-9f40138edfeb?w=400&h=300&fit=crop', 'Palette', 2),
('Business', 'business', 'Develop essential business skills including marketing, management, and entrepreneurship.', 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop', 'TrendingUp', 3),
('Photography', 'photography', 'Capture stunning photos and learn professional photography techniques.', 'https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=400&h=300&fit=crop', 'Camera', 4),
('Music', 'music', 'Learn instruments, music theory, production, and composition.', 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=300&fit=crop', 'Music', 5),
('Marketing', 'marketing', 'Master digital marketing, social media, SEO, and brand strategy.', 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop', 'Briefcase', 6);

-- Create a function to increment enrollment count
CREATE OR REPLACE FUNCTION increment_enrollment_count(course_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE courses 
    SET enrollment_count = enrollment_count + 1 
    WHERE id = course_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to update course rating
CREATE OR REPLACE FUNCTION update_course_rating(course_id UUID)
RETURNS void AS $$
DECLARE
    avg_rating DECIMAL(3,2);
    review_count INTEGER;
BEGIN
    SELECT AVG(rating), COUNT(*) 
    INTO avg_rating, review_count
    FROM reviews 
    WHERE course_id = course_id AND is_public = true;
    
    UPDATE courses 
    SET rating = COALESCE(avg_rating, 0),
        review_count = review_count
    WHERE id = course_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to update course rating when review is added/updated
CREATE OR REPLACE FUNCTION trigger_update_course_rating()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        PERFORM update_course_rating(NEW.course_id);
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        PERFORM update_course_rating(OLD.course_id);
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER reviews_update_course_rating
    AFTER INSERT OR UPDATE OR DELETE ON reviews
    FOR EACH ROW EXECUTE FUNCTION trigger_update_course_rating();

-- Create a function to handle new user registration
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO profiles (id, email, full_name, role)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
        COALESCE(NEW.raw_user_meta_data->>'role', 'student')::user_role
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user registration
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();
