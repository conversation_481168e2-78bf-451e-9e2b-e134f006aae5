<template>
  <div>
    <h1 class="text-3xl font-bold mb-6">Student Dashboard</h1>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div class="bg-card p-6 rounded-lg border">
        <h3 class="font-semibold mb-2">Enrolled Courses</h3>
        <p class="text-2xl font-bold">{{ enrolledCourses.length }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useCoursesStore } from '@/stores/courses'

const authStore = useAuthStore()
const coursesStore = useCoursesStore()

const enrolledCourses = computed(() => {
  const enrolledIds = authStore.user?.enrolledCourses || []
  return coursesStore.courses.filter(course => enrolledIds.includes(course.id))
})
</script>