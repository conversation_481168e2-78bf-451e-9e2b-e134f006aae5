<template>
  <div>
    <h1 class="text-3xl font-bold mb-6">My Enrollments</h1>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <CourseCard v-for="course in enrolledCourses" :key="course.id" v-bind="course" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useCoursesStore } from '@/stores/courses'
import CourseCard from '@/components/CourseCard.vue'

const authStore = useAuthStore()
const coursesStore = useCoursesStore()

const enrolledCourses = computed(() => {
  const enrolledIds = authStore.user?.enrolledCourses || []
  return coursesStore.courses.filter(course => enrolledIds.includes(course.id))
})
</script>