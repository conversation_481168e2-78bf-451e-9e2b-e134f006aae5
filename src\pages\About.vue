<template>
  <div class="min-h-screen bg-background">
    <!-- Hero Section -->
    <section class="relative py-20 overflow-hidden">
      <div class="absolute inset-0 bg-gradient-hero opacity-90" />
      <div class="container relative z-10 text-center text-white">
        <h1 class="text-4xl md:text-5xl font-bold mb-6">
          {{ $t('nav.about') }} LearnHub
        </h1>
        <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto opacity-90">
          Empowering learners worldwide with quality education and expert instruction
        </p>
      </div>
    </section>

    <!-- Mission Section -->
    <section class="py-16">
      <div class="container">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h2 class="text-3xl font-bold mb-6">Our Mission</h2>
            <p class="text-lg text-muted-foreground mb-6">
              At LearnHub, we believe that quality education should be accessible to everyone, everywhere. 
              Our mission is to democratize learning by connecting passionate instructors with eager students 
              around the globe.
            </p>
            <p class="text-lg text-muted-foreground mb-6">
              We're committed to providing a platform where knowledge flows freely, skills are developed 
              continuously, and dreams are transformed into reality through the power of education.
            </p>
            <div class="flex items-center gap-4">
              <div class="flex items-center gap-2">
                <Users class="h-5 w-5 text-primary" />
                <span class="font-semibold">2M+ Students</span>
              </div>
              <div class="flex items-center gap-2">
                <BookOpen class="h-5 w-5 text-primary" />
                <span class="font-semibold">10K+ Courses</span>
              </div>
              <div class="flex items-center gap-2">
                <Award class="h-5 w-5 text-primary" />
                <span class="font-semibold">500+ Instructors</span>
              </div>
            </div>
          </div>
          <div class="relative">
            <img
              src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=600&h=400&fit=crop"
              alt="Students learning together"
              class="rounded-lg shadow-elevated"
            />
          </div>
        </div>
      </div>
    </section>

    <!-- Values Section -->
    <section class="py-16 bg-muted/50">
      <div class="container">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold mb-4">Our Values</h2>
          <p class="text-lg text-muted-foreground max-w-2xl mx-auto">
            These core values guide everything we do and shape the learning experience we provide
          </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div
            v-for="value in values"
            :key="value.title"
            class="bg-card rounded-lg p-6 shadow-sm hover:shadow-elevated transition-shadow"
          >
            <div class="flex items-center gap-3 mb-4">
              <div class="p-2 bg-primary/10 rounded-lg">
                <component :is="value.icon" class="h-6 w-6 text-primary" />
              </div>
              <h3 class="text-xl font-semibold">{{ value.title }}</h3>
            </div>
            <p class="text-muted-foreground">{{ value.description }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Team Section -->
    <section class="py-16">
      <div class="container">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold mb-4">Meet Our Team</h2>
          <p class="text-lg text-muted-foreground max-w-2xl mx-auto">
            Passionate educators and technologists working together to transform online learning
          </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div
            v-for="member in teamMembers"
            :key="member.name"
            class="text-center group"
          >
            <div class="relative mb-4">
              <img
                :src="member.avatar"
                :alt="member.name"
                class="w-32 h-32 rounded-full mx-auto object-cover shadow-lg group-hover:shadow-elevated transition-shadow"
              />
            </div>
            <h3 class="text-xl font-semibold mb-1">{{ member.name }}</h3>
            <p class="text-primary font-medium mb-2">{{ member.role }}</p>
            <p class="text-sm text-muted-foreground">{{ member.bio }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Stats Section -->
    <section class="py-16 bg-primary text-primary-foreground">
      <div class="container">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          <div
            v-for="stat in stats"
            :key="stat.label"
            class="space-y-2"
          >
            <div class="text-3xl md:text-4xl font-bold">{{ stat.value }}</div>
            <div class="text-primary-foreground/80">{{ stat.label }}</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Contact Section -->
    <section class="py-16">
      <div class="container">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold mb-4">Get In Touch</h2>
          <p class="text-lg text-muted-foreground max-w-2xl mx-auto">
            Have questions or want to partner with us? We'd love to hear from you
          </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          <div class="text-center">
            <div class="p-3 bg-primary/10 rounded-lg w-fit mx-auto mb-4">
              <Mail class="h-6 w-6 text-primary" />
            </div>
            <h3 class="font-semibold mb-2">Email Us</h3>
            <p class="text-muted-foreground"><EMAIL></p>
          </div>
          
          <div class="text-center">
            <div class="p-3 bg-primary/10 rounded-lg w-fit mx-auto mb-4">
              <Phone class="h-6 w-6 text-primary" />
            </div>
            <h3 class="font-semibold mb-2">Call Us</h3>
            <p class="text-muted-foreground">+****************</p>
          </div>
          
          <div class="text-center">
            <div class="p-3 bg-primary/10 rounded-lg w-fit mx-auto mb-4">
              <MapPin class="h-6 w-6 text-primary" />
            </div>
            <h3 class="font-semibold mb-2">Visit Us</h3>
            <p class="text-muted-foreground">San Francisco, CA</p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { 
  Users, 
  BookOpen, 
  Award, 
  Heart, 
  Shield, 
  Zap, 
  Target, 
  Globe, 
  Lightbulb,
  Mail,
  Phone,
  MapPin
} from 'lucide-vue-next'

const { t } = useI18n()

const values = ref([
  {
    title: 'Quality First',
    description: 'We maintain the highest standards in course content and instruction to ensure meaningful learning outcomes.',
    icon: Award
  },
  {
    title: 'Accessibility',
    description: 'Education should be available to everyone, regardless of background, location, or financial situation.',
    icon: Globe
  },
  {
    title: 'Innovation',
    description: 'We continuously evolve our platform with cutting-edge technology to enhance the learning experience.',
    icon: Lightbulb
  },
  {
    title: 'Community',
    description: 'Learning is better together. We foster a supportive community of learners and educators.',
    icon: Heart
  },
  {
    title: 'Trust & Safety',
    description: 'We provide a secure, respectful environment where learners can focus on their educational journey.',
    icon: Shield
  },
  {
    title: 'Empowerment',
    description: 'We believe in empowering individuals with skills and knowledge to achieve their personal and professional goals.',
    icon: Zap
  }
])

const teamMembers = ref([
  {
    name: 'Sarah Johnson',
    role: 'CEO & Founder',
    bio: 'Former educator with 15+ years in online learning',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=200&h=200&fit=crop&crop=face'
  },
  {
    name: 'Michael Chen',
    role: 'CTO',
    bio: 'Tech leader passionate about educational technology',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=200&h=200&fit=crop&crop=face'
  },
  {
    name: 'Emily Rodriguez',
    role: 'Head of Education',
    bio: 'Curriculum expert ensuring quality learning experiences',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=200&h=200&fit=crop&crop=face'
  },
  {
    name: 'David Kim',
    role: 'Head of Design',
    bio: 'UX designer creating intuitive learning interfaces',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=200&h=200&fit=crop&crop=face'
  }
])

const stats = ref([
  { value: '2M+', label: 'Active Students' },
  { value: '10K+', label: 'Courses Available' },
  { value: '500+', label: 'Expert Instructors' },
  { value: '95%', label: 'Completion Rate' }
])
</script>
